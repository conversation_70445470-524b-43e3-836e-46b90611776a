'use client';

import { useEffect } from 'react';

import { initializeAccessibility } from '@/lib/accessibility';

export default function ClientInit() {
  useEffect(() => {
    // Initialize accessibility features
    initializeAccessibility();

    // Suppress HMR logging in development
    if (process.env.NODE_ENV === 'development') {
      const originalConsoleLog = console.log;
      console.log = (...args: any[]) => {
        const message = args.join(' ');
        if (message.includes('[Fast Refresh]') || message.includes('report-hmr-latency')) {
          return; // Suppress HMR logging
        }
        originalConsoleLog.apply(console, args);
      };
    }

    // Add global error handler
    window.addEventListener('error', event => {
      console.error('Global error:', event.error);
      // In production, you might want to send this to an error reporting service
    });

    window.addEventListener('unhandledrejection', event => {
      console.error('Unhandled promise rejection:', event.reason);
      // In production, you might want to send this to an error reporting service
    });

    // Cleanup function
    return () => {
      window.removeEventListener('error', () => {});
      window.removeEventListener('unhandledrejection', () => {});
    };
  }, []);

  return null;
}
