'use client';

import { useEffect } from 'react';

import { initializeAccessibility } from '@/lib/accessibility';
import { initializeDevTools } from '@/lib/dev-utils';
import { initializePerformanceOptimizations } from '@/lib/performance';

export default function ClientInit() {
  useEffect(() => {
    // Initialize accessibility features
    initializeAccessibility();

    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Initialize development tools (only in development)
    initializeDevTools();

    // Add global error handler
    window.addEventListener('error', event => {
      console.error('Global error:', event.error);
      // In production, you might want to send this to an error reporting service
    });

    window.addEventListener('unhandledrejection', event => {
      console.error('Unhandled promise rejection:', event.reason);
      // In production, you might want to send this to an error reporting service
    });

    // Cleanup function
    return () => {
      window.removeEventListener('error', () => {});
      window.removeEventListener('unhandledrejection', () => {});
    };
  }, []);

  return null;
}
