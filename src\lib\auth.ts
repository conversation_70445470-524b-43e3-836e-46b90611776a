import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env['JWT_SECRET'] || 'your-super-secret-jwt-key-change-in-production';
const ADMIN_EMAIL = process.env['ADMIN_EMAIL'] || '<EMAIL>';
const ADMIN_PASSWORD_HASH =
  process.env['ADMIN_PASSWORD_HASH'] ||
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6uk6L7LhvG'; // 'admin123'

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'admin';
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export function generateToken(user: AdminUser): string {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role,
    },
    JWT_SECRET,
    { expiresIn: '7d' },
  );
}

export function verifyToken(token: string): AdminUser | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return {
      id: decoded.id,
      email: decoded.email,
      name: decoded.name || 'Admin',
      role: decoded.role,
    };
  } catch (error) {
    return null;
  }
}

export async function authenticateAdmin(
  email: string,
  password: string,
): Promise<AdminUser | null> {
  console.log('authenticateAdmin called with:', { email, password: '***' });
  console.log('ADMIN_EMAIL:', ADMIN_EMAIL);
  console.log('ADMIN_PASSWORD_HASH:', ADMIN_PASSWORD_HASH);

  if (email !== ADMIN_EMAIL) {
    console.log('Email mismatch');
    return null;
  }

  const isValid = await verifyPassword(password, ADMIN_PASSWORD_HASH);
  console.log('Password verification result:', isValid);

  if (!isValid) {
    return null;
  }

  return {
    id: '1',
    email: ADMIN_EMAIL,
    name: 'Admin',
    role: 'admin',
  };
}

// Middleware helper
export function requireAuth(handler: Function) {
  return async (request: Request) => {
    // Import here to avoid circular dependency
    const { getAuthenticatedUser } = await import('./auth-server');
    const user = await getAuthenticatedUser();
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }
    return handler(request, user);
  };
}
