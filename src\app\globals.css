@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap");

/* CSS Variables for custom colors */
:root {
  /* Light mode colors - Much darker base for dramatic sunlight contrast */
  --bg-light: #e8e8ea;
  --surface-light: #dcdce0;
  --text-primary-light: #2a2a2a;
  --text-secondary-light: #5a5a5a;
  --text-muted-light: #7a7a7a;
  --border-light: #bababf;
  --code-light: #d5d5d8;
  --accent-primary: #2cd3c5;
  --accent-hover: #24b5a8;
  --accent-glow: rgba(44, 211, 197, 0.25);

  /* Theme-aware colors */
  --current-bg: var(--bg-light);
  --current-surface: var(--surface-light);
  --current-text-primary: var(--text-primary-light);
  --current-text-secondary: var(--text-secondary-light);
  --current-text-muted: var(--text-muted-light);
  --current-border: var(--border-light);
  --current-code: var(--code-light);
}

.dark {
  /* Dark mode colors */
  --bg-dark: #0d1117;
  --surface-dark: #161b22;
  --text-primary-dark: #e6edf3;
  --text-secondary-dark: #8b949e;
  --text-muted-dark: #6e7681;
  --border-dark: #30363d;
  --code-dark: #1a1f29;

  /* Theme-aware colors for dark mode */
  --current-bg: var(--bg-dark);
  --current-surface: var(--surface-dark);
  --current-text-primary: var(--text-primary-dark);
  --current-text-secondary: var(--text-secondary-dark);
  --current-text-muted: var(--text-muted-dark);
  --current-border: var(--border-dark);
  --current-code: var(--code-dark);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-50 dark:bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  @apply ring-2 ring-primary-500 ring-offset-2 dark:ring-offset-gray-950;
}

/* Selection styles */
::selection {
  background-color: var(--accent-primary);
  color: white;
}

::-moz-selection {
  background-color: var(--accent-primary);
  color: white;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

/* Grid pattern background */
.grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 24px 24px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Glow effect for hover states */
.glow-hover:hover {
  box-shadow: 0 0 20px var(--accent-glow);
  transition: box-shadow 0.3s ease;
}

/* Accent underline */
.accent-underline {
  position: relative;
}

.accent-underline::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-primary);
  transition: width 0.3s ease;
}

.accent-underline:hover::after {
  width: 100%;
}

/* Glass morphism */
.glass {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Z-Index System - Consistent layering throughout the app */
:root {
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-sidebar: 1035;
  --z-sidebar-toggle: 1037;
  --z-sidebar-overlay: 1038;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-mobile-toggle: 1055;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  --z-notification: 1090;

  /* Sidebar width variables for responsive layout - Optimized */
  --sidebar-width-expanded: 220px;
  --sidebar-width-collapsed: 90px;
  --sidebar-width: 0px; /* Default for mobile */
}

/* Responsive sidebar width adjustments */
@media (min-width: 768px) {
  :root {
    --sidebar-width: var(--sidebar-width-collapsed);
  }
}

@media (min-width: 1024px) {
  :root {
    --sidebar-width: var(--sidebar-width-expanded);
  }
}

/* Smooth transitions for sidebar state changes */
.sidebar-transition {
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure grid layouts adapt smoothly to sidebar changes */
.grid {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Skills grid responsive adjustments for sidebar interaction */
@media (min-width: 1024px) {
  /* When sidebar is expanded, ensure proper spacing */
  .skills-grid-responsive {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .skills-grid-responsive {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1536px) {
  .skills-grid-responsive {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Enhanced sidebar toggle button styles */
.sidebar-toggle-enhanced {
  position: relative;
  background: linear-gradient(135deg, rgba(44, 211, 197, 0.1) 0%, rgba(44, 211, 197, 0.05) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.sidebar-toggle-enhanced::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, rgba(44, 211, 197, 0.3), rgba(44, 211, 197, 0.1));
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.sidebar-toggle-enhanced:hover::before {
  background: linear-gradient(135deg, rgba(44, 211, 197, 0.5), rgba(44, 211, 197, 0.2));
}

/* Pulse animation for toggle button */
@keyframes togglePulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.6;
  }
}

.toggle-pulse {
  animation: togglePulse 2s ease-in-out infinite;
}

/* Enhanced geometric shape transitions */
.hexagon {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.diamond {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.octagon {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed sidebar icon enhancements */
.sidebar-icon-collapsed {
  transform: scale(1);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-icon-collapsed:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(44, 211, 197, 0.15);
}

.sidebar-icon-expanded {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animated Theme Toggle Styles */
.theme-toggle-container {
  /* Ensure proper stacking and overflow for light effects */
  position: relative;
  overflow: visible;
}

/* Realistic Sun Light System */
.realistic-sun-light {
  /* Ensure proper layering and blending */
  mix-blend-mode: soft-light;
  pointer-events: none;
}

.sun-illumination-zone {
  /* Smooth transitions for content illumination */
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.illuminatable {
  /* Prepare elements for illumination effects */
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.illuminatable.lit {
  /* Enhanced styling for illuminated elements */
  transform: translateY(-1px);
  z-index: 10;
}

/* Enhanced content illumination effects with dramatic lighting */
.sun-illuminated {
  position: relative;
  transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 10;
}

.sun-illuminated::before {
  content: "";
  position: absolute;
  inset: -15px;
  background: radial-gradient(
    circle at center,
    rgba(252, 211, 77, calc(var(--illumination-intensity, 0) * 0.2)) 0%,
    rgba(245, 158, 11, calc(var(--illumination-intensity, 0) * 0.15)) 30%,
    rgba(255, 193, 7, calc(var(--illumination-intensity, 0) * 0.1)) 60%,
    transparent 80%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: -1;
  opacity: calc(var(--illumination-intensity, 0) * 1.2);
  transition: opacity 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: blur(2px);
}

/* Prevent border flashing during theme transitions */
.theme-transitioning * {
  border-color: inherit !important;
  background-color: inherit !important;
  box-shadow: none !important;
}

/* Enhanced content illumination effects */
.content-glow {
  box-shadow:
    0 0 20px rgba(252, 211, 77, 0.3),
    0 0 40px rgba(245, 158, 11, 0.2),
    inset 0 0 30px rgba(252, 211, 77, 0.1);
  background-color: rgba(252, 211, 77, 0.05);
  border-color: rgba(252, 211, 77, 0.3);
}

/* Sun light ray animations */
@keyframes sunRayRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes sunRayPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(0.6);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.4);
  }
}

@keyframes sunGlow {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.3);
  }
}

/* Moon and stars animations */
@keyframes moonFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-2px) rotate(5deg);
  }
}

@keyframes starsTwinkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable all lighting animations for reduced motion */
  .realistic-sun-light *,
  .sun-illumination-zone,
  .illuminatable,
  .content-glow {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
  }

  /* Hide dynamic lighting effects for users who prefer reduced motion */
  .realistic-sun-light {
    display: none !important;
  }
}

/* Sidebar-specific geometric animations */
@keyframes hexagonPulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
}

@keyframes diamondSlide {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0;
  }
  100% {
    transform: translateX(0) rotate(45deg);
    opacity: 1;
  }
}

@keyframes circuitLine {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes logoRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Geometric shape utilities */
.hexagon {
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  will-change: transform;
}

.diamond {
  transform: rotate(45deg);
  will-change: transform;
}

.octagon {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
  will-change: transform;
}

/* Notification and Toast positioning */
.notification-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: var(--z-notification);
  pointer-events: none;
}

.notification-container > * {
  pointer-events: auto;
}

.toast-container {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: var(--z-toast);
  pointer-events: none;
}

.toast-container > * {
  pointer-events: auto;
}

/* Modal and overlay positioning */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal-backdrop);
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-modal);
  max-height: 90vh;
  overflow-y: auto;
}

/* Dropdown positioning */
.dropdown-menu {
  position: absolute;
  z-index: var(--z-dropdown);
}

/* Popover positioning */
.popover {
  position: absolute;
  z-index: var(--z-popover);
}

/* Tooltip positioning */
.tooltip {
  position: absolute;
  z-index: var(--z-tooltip);
}

/* Loading spinner overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal-backdrop);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.dark .loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Form validation message positioning */
.form-error {
  position: relative;
  z-index: 1;
}

/* Code block styling */
.code-block {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto;
}

/* Terminal styling */
.terminal {
  @apply bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm;
}

.terminal::before {
  content: "$ ";
  @apply text-green-500;
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
