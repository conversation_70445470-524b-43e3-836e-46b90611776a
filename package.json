{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "echo \"No tests specified\" && exit 0", "test:watch": "echo \"No tests specified\" && exit 0", "analyze": "ANALYZE=true npm run build", "clean": "rm -rf .next out dist", "prebuild": "npm run clean && npm run type-check", "postbuild": "echo \"Build completed successfully\"", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "npm update", "validate": "npm run type-check && npm run lint && npm run format:check", "prepare": "husky install || true"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "framer-motion": "^12.23.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "postcss": "^8.5.6", "prettier": "^3.1.1", "tailwindcss": "^3.4.17", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check"}}}