'use client';

import { motion, AnimatePresence } from 'framer-motion';
import React, { useEffect, useState } from 'react';

import { useTheme } from '@/lib/theme-context';

export default function SunlightGrid() {
  const { theme } = useTheme();
  const [illuminatedColumns, setIlluminatedColumns] = useState<number[]>([]);
  const [mounted, setMounted] = useState(false);

  // Generate random illuminated columns
  const generateIlluminatedColumns = () => {
    const columns: number[] = [];
    const totalColumns = 12;
    const illuminatedCount = 3;

    while (columns.length < illuminatedCount) {
      const randomColumn = Math.floor(Math.random() * totalColumns);
      if (!columns.includes(randomColumn)) {
        columns.push(randomColumn);
      }
    }

    return columns.sort((a, b) => a - b);
  };

  useEffect(() => {
    setMounted(true);

    // Initial illumination
    setIlluminatedColumns(generateIlluminatedColumns());

    // Change illuminated columns every 12 seconds for subtle dynamic lighting
    const interval = setInterval(() => {
      setIlluminatedColumns(generateIlluminatedColumns());
    }, 12000);

    return () => clearInterval(interval);
  }, []);

  // Don't render on server or in dark mode
  if (!mounted || theme === 'dark') {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-[1] overflow-hidden sunlight-grid">
      {/* Grid Container */}
      <div className="absolute inset-0 grid grid-cols-12 gap-0 w-full h-full">
        {[...Array(12)].map((_, columnIndex) => (
          <div key={columnIndex} className="relative h-full">
            <AnimatePresence>
              {illuminatedColumns.includes(columnIndex) && (
                <motion.div
                  initial={{ opacity: 0, scaleX: 0.8 }}
                  animate={{
                    opacity: [0, 0.15, 0.25, 0.15, 0],
                    scaleX: [0.8, 1, 1.1, 1, 0.8],
                  }}
                  exit={{ opacity: 0, scaleX: 0.8 }}
                  transition={{
                    duration: 6 + columnIndex * 0.3, // Slight variation in timing
                    repeat: Infinity,
                    ease: 'easeInOut',
                    times: [0, 0.2, 0.5, 0.8, 1],
                    delay: columnIndex * 0.2, // Stagger the start times
                  }}
                  className="absolute inset-0 h-full"
                  style={{
                    background: `linear-gradient(
                      180deg,
                      rgba(251, 191, 36, 0.08) 0%,
                      rgba(251, 191, 36, 0.12) 20%,
                      rgba(255, 159, 28, 0.15) 40%,
                      rgba(251, 191, 36, 0.12) 60%,
                      rgba(251, 191, 36, 0.08) 80%,
                      rgba(251, 191, 36, 0.04) 100%
                    )`,
                    filter: 'blur(1px)',
                  }}
                />
              )}
            </AnimatePresence>

            {/* Subtle grid line indicator */}
            {illuminatedColumns.includes(columnIndex) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{
                  opacity: [0, 0.1, 0.2, 0.1, 0],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 0.5,
                }}
                className="absolute left-0 top-0 w-px h-full bg-gradient-to-b from-transparent via-yellow-300/20 to-transparent"
              />
            )}
          </div>
        ))}
      </div>

      {/* Additional light rays from top-right (where sun is positioned) */}
      <AnimatePresence>
        {illuminatedColumns.map((columnIndex, i) => (
          <motion.div
            key={`ray-${columnIndex}`}
            initial={{ opacity: 0, rotate: 0 }}
            animate={{
              opacity: [0, 0.08, 0.12, 0.08, 0],
              rotate: [0, 2, -1, 1, 0],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.5,
            }}
            className="absolute"
            style={{
              top: '6%',
              right: '6%',
              width: '2px',
              height: `${60 + columnIndex * 5}%`,
              background: `linear-gradient(
                to bottom,
                rgba(251, 191, 36, 0.3) 0%,
                rgba(255, 159, 28, 0.2) 30%,
                rgba(251, 191, 36, 0.1) 60%,
                transparent 100%
              )`,
              transformOrigin: 'top center',
              transform: `rotate(${-45 + columnIndex * 8}deg)`,
              filter: 'blur(0.5px)',
            }}
          />
        ))}
      </AnimatePresence>

      {/* Ambient light overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{
          opacity: [0, 0.03, 0.05, 0.03, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(
            ellipse 80% 60% at 85% 15%,
            rgba(251, 191, 36, 0.08) 0%,
            rgba(255, 159, 28, 0.04) 40%,
            transparent 70%
          )`,
        }}
      />
    </div>
  );
}
