// SEO utilities and structured data helpers
import type { Metadata } from 'next';

import type { PersonalInfo, Project } from '@/types';
import { Experience, Education } from '@/types';

// Structured data types
interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: unknown;
}

interface PersonStructuredData extends StructuredData {
  '@type': 'Person';
  name: string;
  jobTitle: string;
  description: string;
  url: string;
  image?: string;
  email?: string;
  telephone?: string;
  address?: {
    '@type': 'PostalAddress';
    addressLocality: string;
    addressCountry: string;
  };
  sameAs: string[];
  knowsAbout: string[];
}

interface WebsiteStructuredData extends StructuredData {
  '@type': 'WebSite';
  name: string;
  description: string;
  url: string;
  author: {
    '@type': 'Person';
    name: string;
  };
  potentialAction: {
    '@type': 'SearchAction';
    target: string;
    'query-input': string;
  };
}

interface ProjectStructuredData extends StructuredData {
  '@type': 'CreativeWork';
  name: string;
  description: string;
  url?: string;
  author: {
    '@type': 'Person';
    name: string;
  };
  dateCreated: string;
  programmingLanguage: string[];
  genre: string;
}

// Generate structured data for person/portfolio
export function generatePersonStructuredData(
  personalInfo: PersonalInfo,
  skills: string[] = [],
): PersonStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: personalInfo.name,
    jobTitle: personalInfo.title,
    description: personalInfo.bio,
    url: personalInfo.socialLinks.find(link => link.name.toLowerCase() === 'website')?.url || '',
    image: personalInfo.profileImage,
    email: personalInfo.email,
    telephone: personalInfo.phone,
    address: personalInfo.location
      ? {
        '@type': 'PostalAddress',
        addressLocality: personalInfo.location.split(',')[0]?.trim() || '',
        addressCountry: personalInfo.location.split(',')[1]?.trim() || '',
      }
      : undefined,
    sameAs: personalInfo.socialLinks.map(link => link.url),
    knowsAbout: skills,
  };
}

// Generate structured data for website
export function generateWebsiteStructuredData(siteInfo: {
  name: string;
  description: string;
  url: string;
  authorName: string;
}): WebsiteStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteInfo.name,
    description: siteInfo.description,
    url: siteInfo.url,
    author: {
      '@type': 'Person',
      name: siteInfo.authorName,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteInfo.url}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };
}

// Generate structured data for projects
export function generateProjectStructuredData(
  project: Project,
  authorName: string,
): ProjectStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'CreativeWork',
    name: project.title,
    description: project.description,
    url: project.liveUrl || project.githubUrl,
    author: {
      '@type': 'Person',
      name: authorName,
    },
    dateCreated: project.year.toString(),
    programmingLanguage: [...project.technologies],
    genre: project.category,
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url: string }>,
): StructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// Enhanced metadata generation
export function generatePageMetadata(options: {
  title: string;
  description: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'profile';
  twitterCard?: 'summary' | 'summary_large_image';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  noIndex?: boolean;
}): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonicalUrl,
    ogImage,
    ogType = 'website',
    twitterCard = 'summary_large_image',
    author,
    publishedTime,
    modifiedTime,
    noIndex = false,
  } = options;

  return {
    title,
    description,
    keywords: keywords.join(', '),
    authors: author ? [{ name: author }] : undefined,
    creator: author,
    publisher: author,

    // Canonical URL
    alternates: canonicalUrl
      ? {
        canonical: canonicalUrl,
      }
      : undefined,

    // Open Graph
    openGraph: {
      title,
      description,
      type: ogType,
      url: canonicalUrl,
      images: ogImage
        ? [
          {
            url: ogImage,
            width: 1200,
            height: 630,
            alt: title,
          },
        ]
        : undefined,
      publishedTime,
      modifiedTime,
      authors: author ? [author] : undefined,
    },

    // Twitter
    twitter: {
      card: twitterCard,
      title,
      description,
      images: ogImage ? [ogImage] : undefined,
      creator: author ? `@${author}` : undefined,
    },

    // Robots
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // Additional meta tags
    other: {
      'theme-color': '#2CD3C5',
      'color-scheme': 'light dark',
      'format-detection': 'telephone=no',
    },
  };
}

// Generate sitemap data
export function generateSitemapData(
  pages: Array<{
    url: string;
    lastModified?: Date;
    changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
    priority?: number;
  }>,
) {
  return pages.map(page => ({
    url: page.url,
    lastModified: page.lastModified || new Date(),
    changeFrequency: page.changeFrequency || 'monthly',
    priority: page.priority || 0.5,
  }));
}

// SEO analysis helpers
export function analyzeSEO(content: {
  title?: string;
  description?: string;
  headings?: string[];
  content?: string;
  images?: Array<{ alt?: string; src: string }>;
}): {
  score: number;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  let score = 100;

  // Title analysis
  if (!content.title) {
    issues.push('Missing page title');
    score -= 20;
  } else if (content.title.length < 30 || content.title.length > 60) {
    recommendations.push('Title should be 30-60 characters long');
    score -= 5;
  }

  // Description analysis
  if (!content.description) {
    issues.push('Missing meta description');
    score -= 15;
  } else if (content.description.length < 120 || content.description.length > 160) {
    recommendations.push('Meta description should be 120-160 characters long');
    score -= 5;
  }

  // Heading analysis
  if (!content.headings?.length) {
    issues.push('No headings found');
    score -= 10;
  } else {
    const h1Count = content.headings.filter(h => h.startsWith('h1')).length;
    if (h1Count === 0) {
      issues.push('Missing H1 heading');
      score -= 15;
    } else if (h1Count > 1) {
      issues.push('Multiple H1 headings found');
      score -= 10;
    }
  }

  // Image analysis
  if (content.images?.length) {
    const imagesWithoutAlt = content.images.filter(img => !img.alt);
    if (imagesWithoutAlt.length > 0) {
      issues.push(`${imagesWithoutAlt.length} images missing alt text`);
      score -= imagesWithoutAlt.length * 5;
    }
  }

  // Content analysis
  if (content.content) {
    const wordCount = content.content.split(/\s+/).length;
    if (wordCount < 300) {
      recommendations.push('Consider adding more content (aim for 300+ words)');
      score -= 5;
    }
  }

  return {
    score: Math.max(0, score),
    issues,
    recommendations,
  };
}

// Inject structured data into page
export function injectStructuredData(data: StructuredData | StructuredData[]) {
  if (typeof window === 'undefined') {
    return;
  }

  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(Array.isArray(data) ? data : [data], null, 2);

  // Remove existing structured data
  const existing = document.querySelector('script[type="application/ld+json"]');
  if (existing) {
    existing.remove();
  }

  document.head.appendChild(script);
}
