import type { Metadata } from 'next';
import { Inter, JetBrains_Mono, Space_Grotesk } from 'next/font/google';

import './globals.css';
import ClientInit from '@/components/ClientInit';
import ConditionalLayout from '@/components/layout/ConditionalLayout';
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { seoConfig } from '@/data/portfolio';
import { generatePageMetadata } from '@/lib/seo';
import { SidebarProvider } from '@/lib/sidebar-context';
import { ThemeProvider } from '@/lib/theme-context';

// Font configuration
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  preload: true,
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
  preload: true,
});

const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  variable: '--font-space-grotesk',
  display: 'swap',
  preload: true,
});

// Enhanced metadata for SEO
export const metadata: Metadata = generatePageMetadata({
  title: seoConfig.title,
  description: seoConfig.description,
  keywords: seoConfig.keywords,
  canonicalUrl: seoConfig.siteUrl,
  ogImage: seoConfig.image,
  author: seoConfig.author,
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#2CD3C5" />
        <meta name="color-scheme" content="light dark" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} ${spaceGrotesk.variable} font-sans antialiased`}
      >
        <ErrorBoundary>
          <ThemeProvider>
            <ClientInit />
            <SidebarProvider>
              <ConditionalLayout>{children}</ConditionalLayout>
            </SidebarProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
