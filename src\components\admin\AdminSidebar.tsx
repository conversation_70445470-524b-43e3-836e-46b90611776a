'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  User,
  FolderOpen,
  Mail,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  Shield,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

interface AdminSidebarProps {
  user: {
    name: string;
    email: string;
  };
}

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard, description: 'Overview & analytics' },
  { name: 'About', href: '/admin/about', icon: User, description: 'Personal information' },
  {
    name: 'Projects',
    href: '/admin/projects',
    icon: FolderOpen,
    description: 'Portfolio projects',
  },
  { name: 'Messages', href: '/admin/contacts', icon: Mail, description: 'Contact form messages' },
  { name: 'Settings', href: '/admin/settings', icon: Settings, description: 'Site configuration' },
];

export default function AdminSidebar({ user }: AdminSidebarProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      router.push('/admin/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const sidebarContent = (
    <div className="flex flex-col h-full min-h-screen bg-background-light dark:bg-background-dark">
      {/* Logo/Header */}
      <div className="flex items-center justify-between p-6 border-b border-border-light dark:border-border-dark bg-surface-light dark:bg-surface-dark">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-text-primary-light dark:text-text-primary-dark">
              Admin Panel
            </h2>
            <p className="text-xs text-text-secondary-light dark:text-text-secondary-dark">
              Portfolio Management
            </p>
          </div>
        </div>
        <button
          onClick={() => setIsMobileMenuOpen(false)}
          className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <X className="w-5 h-5 text-text-secondary-light dark:text-text-secondary-dark" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
        {navigation.map(item => {
          const isActive = pathname === item.href;
          return (
            <motion.div key={item.name} whileHover={{ x: 4 }} whileTap={{ scale: 0.98 }}>
              <Link
                href={item.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`group flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                  isActive
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 shadow-sm border border-primary-200 dark:border-primary-800'
                    : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-gray-100 dark:hover:bg-gray-700/50 hover:text-text-primary-light dark:hover:text-text-primary-dark'
                }`}
              >
                <item.icon
                  className={`w-5 h-5 transition-colors ${
                    isActive
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-text-muted-light dark:text-text-muted-dark group-hover:text-text-primary-light dark:group-hover:text-text-primary-dark'
                  }`}
                />
                <div className="flex-1 min-w-0">
                  <span className="font-medium text-sm">{item.name}</span>
                  <p className="text-xs text-text-muted-light dark:text-text-muted-dark mt-0.5 truncate">
                    {item.description}
                  </p>
                </div>
                {isActive && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="w-2 h-2 bg-primary-500 rounded-full"
                    initial={false}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </Link>
            </motion.div>
          );
        })}
      </nav>

      {/* User Profile & Actions */}
      <div className="flex-shrink-0 p-4 border-t border-border-light dark:border-border-dark">
        {/* User Info */}
        <div className="flex items-center space-x-3 px-4 py-3 mb-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center shadow-md">
            <span className="text-white font-bold text-sm">
              {user.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-text-primary-light dark:text-text-primary-dark truncate">
              {user.name}
            </p>
            <p className="text-xs text-text-secondary-light dark:text-text-secondary-dark truncate">
              {user.email}
            </p>
          </div>
        </div>

        <div className="space-y-1">
          <motion.div whileHover={{ x: 2 }} whileTap={{ scale: 0.98 }}>
            <Link
              href="/"
              target="_blank"
              className="flex items-center space-x-3 px-4 py-2.5 text-text-secondary-light dark:text-text-secondary-dark hover:bg-gray-100 dark:hover:bg-gray-700/50 hover:text-text-primary-light dark:hover:text-text-primary-dark rounded-lg transition-all duration-200 group"
            >
              <Home className="w-4 h-4 group-hover:text-primary-500 transition-colors" />
              <span className="text-sm font-medium">View Site</span>
            </Link>
          </motion.div>
          <motion.div whileHover={{ x: 2 }} whileTap={{ scale: 0.98 }}>
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-4 py-2.5 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 group"
            >
              <LogOut className="w-4 h-4 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">Sign Out</span>
            </button>
          </motion.div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Menu Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsMobileMenuOpen(true)}
        className="lg:hidden fixed top-4 left-4 z-sticky p-3 bg-surface-light dark:bg-surface-dark rounded-xl shadow-lg border border-border-light dark:border-border-dark backdrop-blur-sm"
      >
        <Menu className="w-6 h-6 text-text-primary-light dark:text-text-primary-dark" />
      </motion.button>

      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:z-fixed border-r border-border-light dark:border-border-dark shadow-sm">
        {sidebarContent}
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMobileMenuOpen(false)}
              className="lg:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-modal-backdrop"
            />

            {/* Sidebar */}
            <motion.div
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 30,
                opacity: { duration: 0.2 },
              }}
              className="lg:hidden fixed inset-y-0 left-0 w-64 border-r border-border-light dark:border-border-dark shadow-2xl z-modal bg-background-light dark:bg-background-dark"
            >
              {sidebarContent}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
