'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, AlertTriangle, Info, X, AlertCircle } from 'lucide-react';
import React, { useEffect } from 'react';

import { cn } from '@/lib/utils';

export interface ToastProps {
  id?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  isVisible: boolean;
  onDismiss: () => void;
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right';
  showCloseButton?: boolean;
}

const toastIcons = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const toastStyles = {
  success: {
    bg: 'bg-green-500',
    text: 'text-white',
    icon: 'text-white',
  },
  error: {
    bg: 'bg-red-500',
    text: 'text-white',
    icon: 'text-white',
  },
  warning: {
    bg: 'bg-yellow-500',
    text: 'text-white',
    icon: 'text-white',
  },
  info: {
    bg: 'bg-blue-500',
    text: 'text-white',
    icon: 'text-white',
  },
};

const positionStyles = {
  'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
  'top-right': 'top-4 right-4',
  'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  'bottom-right': 'bottom-4 right-4',
};

export default function Toast({
  type = 'info',
  title,
  message,
  duration = 5000,
  isVisible,
  onDismiss,
  position = 'top-center',
  showCloseButton = true,
}: ToastProps) {
  const IconComponent = toastIcons[type];
  const styles = toastStyles[type];

  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onDismiss, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onDismiss]);

  const getAnimationProps = () => {
    const isTop = position.includes('top');
    const isCenter = position.includes('center');

    if (isCenter) {
      return {
        initial: { opacity: 0, y: isTop ? -50 : 50, x: '-50%' },
        animate: { opacity: 1, y: 0, x: '-50%' },
        exit: { opacity: 0, y: isTop ? -50 : 50, x: '-50%' },
      };
    } else {
      return {
        initial: { opacity: 0, y: isTop ? -50 : 50, x: 0 },
        animate: { opacity: 1, y: 0, x: 0 },
        exit: { opacity: 0, y: isTop ? -50 : 50, x: 0 },
      };
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          {...getAnimationProps()}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className={cn(
            'fixed z-toast px-6 py-4 rounded-xl shadow-lg flex items-center space-x-3 max-w-md',
            styles.bg,
            positionStyles[position],
          )}
        >
          <IconComponent className={cn('w-5 h-5 flex-shrink-0', styles.icon)} />

          <div className="flex-1 min-w-0">
            {title && <p className={cn('font-semibold text-sm', styles.text)}>{title}</p>}
            <p className={cn('text-sm', styles.text, title && 'mt-1')}>{message}</p>
          </div>

          {showCloseButton && (
            <button
              onClick={onDismiss}
              className={cn(
                'flex-shrink-0 p-1 rounded-full hover:bg-black/10 transition-colors',
                styles.text,
              )}
              aria-label="Close notification"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Toast container component for managing multiple toasts
export function ToastContainer({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <div className={cn('toast-container', className)}>{children}</div>;
}

// Hook for managing toast state
export function useToast() {
  const [toasts, setToasts] = React.useState<Array<ToastProps & { id: string }>>([]);

  const showToast = (toast: Omit<ToastProps, 'id' | 'isVisible' | 'onDismiss'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = {
      ...toast,
      id,
      isVisible: true,
      onDismiss: () => dismissToast(id),
    };
    setToasts(prev => [...prev, newToast]);
  };

  const dismissToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const dismissAll = () => {
    setToasts([]);
  };

  return {
    toasts,
    showToast,
    dismissToast,
    dismissAll,
  };
}
