'use client';

import { motion } from 'framer-motion';
import { Home, ArrowLeft, Search, Code, Terminal } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import Button from '@/components/ui/Button';

export default function NotFound() {
  const codeLines = [
    'function findPage(url) {',
    '  const page = database.find(url);',
    '  if (!page) {',
    "    throw new Error('404: Page not found');",
    '  }',
    '  return page;',
    '}',
    '',
    "// Oops! This page doesn't exist",
    "findPage('/this-page'); // 💥 Error!",
  ];

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Animated 404 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="mb-8"
        >
          <div className="relative">
            <h1 className="text-8xl md:text-9xl font-bold font-display gradient-text mb-4">404</h1>

            {/* Floating elements */}
            <motion.div
              animate={{
                y: [0, -10, 0],
                rotate: [0, 5, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              className="absolute -top-4 -left-4 text-primary-500/30"
            >
              <Code size={40} />
            </motion.div>

            <motion.div
              animate={{
                y: [0, 10, 0],
                rotate: [0, -5, 0],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: 1,
              }}
              className="absolute -top-4 -right-4 text-accent-500/30"
            >
              <Terminal size={35} />
            </motion.div>
          </div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Page Not Found
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Looks like you've ventured into uncharted territory. The page you're looking for doesn't
            exist.
          </p>
        </motion.div>

        {/* Code Block */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-12 max-w-2xl mx-auto"
        >
          <div className="bg-gray-900 rounded-lg p-6 text-left overflow-hidden">
            {/* Terminal Header */}
            <div className="flex items-center space-x-2 mb-4 pb-3 border-b border-gray-700">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <span className="text-sm text-gray-400 ml-4">error.js</span>
            </div>

            {/* Code */}
            <div className="font-mono text-sm">
              {codeLines.map((line, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                  className={`${
                    line.includes('Error')
                      ? 'text-red-400'
                      : line.includes('function') ||
                          line.includes('const') ||
                          line.includes('if') ||
                          line.includes('throw') ||
                          line.includes('return')
                        ? 'text-blue-400'
                        : line.includes('//')
                          ? 'text-green-400'
                          : line.includes("'404")
                            ? 'text-yellow-400'
                            : 'text-gray-300'
                  }`}
                >
                  <span className="text-gray-500 mr-4 select-none">
                    {String(index + 1).padStart(2, ' ')}
                  </span>
                  {line || '\u00A0'}
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <Link href="/">
            <Button size="lg" icon={<Home size={20} />} className="min-w-[200px]">
              Go Home
            </Button>
          </Link>

          <Button
            variant="outline"
            size="lg"
            icon={<ArrowLeft size={20} />}
            onClick={() => window.history.back()}
            className="min-w-[200px]"
          >
            Go Back
          </Button>
        </motion.div>

        {/* Helpful Links */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700"
        >
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Or try one of these popular pages:
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              { name: 'About', href: '/about' },
              { name: 'Projects', href: '/projects' },
              { name: 'Contact', href: '/contact' },
            ].map(link => (
              <Link
                key={link.name}
                href={link.href}
                className="text-primary-500 dark:text-primary-400 hover:text-primary-600 dark:hover:text-primary-300 transition-colors font-medium"
              >
                {link.name}
              </Link>
            ))}
          </div>
        </motion.div>

        {/* Fun Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-8"
        >
          <p className="text-sm text-gray-500 dark:text-gray-400">
            💡 Pro tip: Check the URL for typos, or use the navigation menu above
          </p>
        </motion.div>
      </div>
    </div>
  );
}
