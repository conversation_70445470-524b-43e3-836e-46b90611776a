'use client';

import { useEffect, useState } from 'react';

import SidebarNavigation from './SidebarNavigation';

export default function ClientSidebar() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <aside className="fixed left-0 top-0 h-full w-[220px] bg-surface-light dark:bg-surface-dark border-r border-border-light dark:border-border-dark shadow-xl z-[var(--z-sidebar)]">
        <div className="p-6 border-b border-border-light dark:border-border-dark">
          <div className="flex items-center space-x-3">
            <div className="octagon w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-hover flex items-center justify-center">
              <div className="text-white text-xl font-bold">P</div>
            </div>
            <div className="text-xl font-bold gradient-text font-space-grotesk">Portfolio</div>
          </div>
        </div>
      </aside>
    );
  }

  return <SidebarNavigation />;
}
