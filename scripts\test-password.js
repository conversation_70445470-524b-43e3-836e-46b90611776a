const bcrypt = require('bcryptjs');

async function testPassword() {
  const hash = '$2b$12$fVoB6XZ8QezEARozoV4BJO1R59rzEE1bCaMMMMZWiybp5W5/RCPWSC';
  const passwords = ['admin123', 'admin', 'password', 'test123'];
  
  console.log('Testing passwords against hash:', hash);
  console.log('');
  
  for (const password of passwords) {
    const isValid = await bcrypt.compare(password, hash);
    console.log(`Password "${password}": ${isValid ? 'VALID' : 'INVALID'}`);
  }
  
  console.log('');
  console.log('If none of these work, the hash was generated with a different password.');
  console.log('Use: node scripts/generate-password-hash.js <your-password>');
}

testPassword().catch(console.error);
