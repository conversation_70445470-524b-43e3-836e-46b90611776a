'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  UserCircle,
  Terminal,
  Sparkles,
  Rocket,
  Send,
  Mail,
  Linkedin,
  Github,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState, useEffect } from 'react';

// Alternative icon options available:
// Zap, Home, User, Briefcase, Mail, Globe, Star, Heart, Target, Compass, Layers, MessageCircle, Phone, AtSign

import { useSidebar } from '@/lib/sidebar-context';
import { useTheme } from '@/lib/theme-context';
import { cn } from '@/lib/utils';

// 🎨 ICON SET OPTIONS - Choose your preferred style:

// Terminal Commands Navigation
const navItems = [
  {
    name: 'cd ~/home',
    href: '/',
    icon: Sparkles,
    command: 'cd ~/home',
    description: 'Navigate to home directory',
  },
  {
    name: 'whoami',
    href: '/about',
    icon: UserCircle,
    command: 'whoami',
    description: 'Display user information',
  },
  {
    name: 'ls projects/',
    href: '/projects',
    icon: Rocket,
    command: 'ls projects/',
    description: 'List project files',
  },
  {
    name: 'ping',
    href: '/contact',
    icon: Send,
    command: 'ping',
    description: 'Test connection to contact',
  },
];

// Alternative Option 1: Classic & Professional
// const navItems = [
//   { name: 'Home', href: '/', icon: Home, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: User, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Briefcase, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: Mail, command: 'ping' },
// ];

// Alternative Option 2: Tech & Energetic
// const navItems = [
//   { name: 'Home', href: '/', icon: Zap, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: UserCircle, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Layers, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: MessageCircle, command: 'ping' },
// ];

// Alternative Option 3: Creative & Unique
// const navItems = [
//   { name: 'Home', href: '/', icon: Star, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: Heart, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Target, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: AtSign, command: 'ping' },
// ];

// Alternative Option 4: Navigation & Exploration
// const navItems = [
//   { name: 'Home', href: '/', icon: Compass, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: UserCircle, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Globe, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: Phone, command: 'ping' },
// ];

interface SidebarNavigationProps {
  className?: string;
}

export default function SidebarNavigation({ className }: SidebarNavigationProps) {
  const {
    isMobileOpen,
    setIsMobileOpen,
    toggleMobile,
  } = useSidebar();
  const { theme } = useTheme();

  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [currentCommand, setCurrentCommand] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const pathname = usePathname();

  // Typing animation function
  const typeCommand = async (command: string) => {
    setIsTyping(true);
    setCurrentCommand('');

    for (let i = 0; i <= command.length; i++) {
      setCurrentCommand(command.slice(0, i));
      await new Promise(resolve => setTimeout(resolve, 50)); // Typing speed
    }

    // Add to command history
    setCommandHistory(prev => [...prev.slice(-4), command]); // Keep last 5 commands

    setTimeout(() => {
      setIsTyping(false);
      setCurrentCommand('');
    }, 1000);
  };

  // Handle navigation with typing effect
  const handleNavigation = (item: (typeof navItems)[0]) => {
    typeCommand(item.command);
    setIsMobileOpen(false);
  };



  // Initialize current page command on load
  useEffect(() => {
    const currentItem = navItems.find(item => item.href === pathname);
    if (currentItem && commandHistory.length === 0) {
      setCommandHistory([currentItem.command]);
    }
  }, [pathname, commandHistory.length]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Close mobile menu on Escape
      if (e.key === 'Escape' && isMobileOpen) {
        setIsMobileOpen(false);
        return;
      }

      // Navigate with arrow keys when sidebar is focused
      if (document.activeElement?.closest('[role="navigation"]')) {
        const navLinks = document.querySelectorAll('[role="menuitem"]');
        const currentIndex = Array.from(navLinks).findIndex(
          link => link === document.activeElement,
        );

        if (e.key === 'ArrowDown' && currentIndex < navLinks.length - 1) {
          e.preventDefault();
          (navLinks[currentIndex + 1] as HTMLElement).focus();
        } else if (e.key === 'ArrowUp' && currentIndex > 0) {
          e.preventDefault();
          (navLinks[currentIndex - 1] as HTMLElement).focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileOpen]);

  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Listen for custom event to open mobile sidebar
  useEffect(() => {
    const handleOpenMobileSidebar = () => {
      if (window.innerWidth < 768) {
        setIsMobileOpen(true);
      }
    };

    window.addEventListener('openMobileSidebar', handleOpenMobileSidebar);
    return () => window.removeEventListener('openMobileSidebar', handleOpenMobileSidebar);
  }, []);

  // Touch gesture handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      return;
    }

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (window.innerWidth < 768) {
      if (isLeftSwipe && isMobileOpen) {
        setIsMobileOpen(false);
      } else if (isRightSwipe && !isMobileOpen) {
        setIsMobileOpen(true);
      }
    }
  };



  return (
    <>
      {/* Skip Link for Accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only fixed top-4 left-4 z-[9999] bg-accent-primary text-white px-4 py-2 rounded-lg font-medium"
      >
        Skip to main content
      </a>

      {/* Enhanced Mobile Toggle Button */}
      <motion.button
        whileHover={{
          scale: 1.1,
          boxShadow: '0 8px 25px rgba(44, 211, 197, 0.4)',
          backgroundColor: 'rgba(44, 211, 197, 0.15)',
        }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleMobile}
        className="md:hidden fixed top-4 left-4 z-[var(--z-mobile-toggle)] p-4 rounded-2xl bg-gradient-to-br from-surface-light to-surface-light/80 dark:from-surface-dark dark:to-surface-dark/80 border-2 border-accent-primary/30 shadow-xl transition-all duration-300 backdrop-blur-md group relative overflow-hidden"
        aria-label="Toggle navigation"
        title="Toggle navigation menu"
      >
        {/* Animated Background Glow */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-accent-primary/10 to-accent-primary/5"
          animate={{
            opacity: [0.3, 0.7, 0.3],
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        {/* Icon with Enhanced Animation */}
        <motion.div
          animate={{
            rotate: isMobileOpen ? 180 : 0,
            scale: isMobileOpen ? 1.1 : 1,
          }}
          transition={{
            duration: 0.4,
            type: 'spring',
            stiffness: 200,
            damping: 20,
          }}
          className="relative z-10"
        >
          <Terminal
            size={24}
            className="text-accent-primary group-hover:text-accent-primary drop-shadow-sm"
            strokeWidth={2}
          />
        </motion.div>

        {/* Pulse Ring Effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl border-2 border-accent-primary/40"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0, 0.6, 0],
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </motion.button>

      {/* Mobile Overlay */}
      <AnimatePresence>
        {isMobileOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsMobileOpen(false)}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[var(--z-sidebar-overlay)] md:hidden"
            style={{ touchAction: 'none' }}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <aside
        className={cn(
          'fixed left-0 top-0 h-screen w-[220px] z-[var(--z-sidebar)] flex flex-col',
          'md:translate-x-0 transition-all duration-300 ease-in-out will-change-transform',
          'font-mono overflow-hidden',
          'bg-black border-r-2 border-gray-600 shadow-2xl shadow-green-400/20',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
          className,
        )}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          height: '100vh',
          width: '220px',
          zIndex: 1035,
        }}
        role="navigation"
        aria-label="Main navigation"
        aria-expanded={isMobileOpen}
        aria-hidden={!isMobileOpen && window?.innerWidth < 768}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Terminal Window Header - Minimal */}
        <div className="bg-gray-800 border-b border-gray-600">
          {/* Terminal Title Bar - Only Window Controls */}
          <div className="flex items-center justify-center bg-gray-700 px-3 py-2">
            <div className="flex space-x-1">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Terminal Navigation */}
        <nav
          className="flex-1 p-4 space-y-1 bg-black font-mono overflow-y-auto"
          role="menu"
          aria-label="Terminal navigation"
        >
          {navItems.map((item, index) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: index * 0.08,
                  duration: 0.4,
                  ease: 'easeOut' as const,
                }}
                onHoverStart={() => setHoveredItem(item.name)}
                onHoverEnd={() => setHoveredItem(null)}
              >
                <Link
                  href={item.href}
                  onClick={() => handleNavigation(item)}
                  className={cn(
                    'group relative flex items-center transition-all duration-300 font-mono text-sm',
                    'hover:bg-green-400/10 focus:outline-none focus:ring-2 focus:ring-green-400/50',
                    'rounded border border-transparent px-2 py-1',
                    isActive
                      ? 'bg-green-400/20 border-green-400/30 shadow-lg shadow-green-400/20 text-green-300'
                      : 'text-green-400 hover:text-green-300',
                    // Terminal-style spacing
                    'space-x-2',
                  )}
                  role="menuitem"
                  aria-current={isActive ? 'page' : undefined}
                  aria-label={`Execute command: ${item.command}`}
                >
                  {/* Terminal Active Indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="terminalIndicator"
                      className="absolute left-0 top-1/2 -translate-y-1/2 text-green-400"
                      initial={false}
                      transition={{ type: 'spring' as const, bounce: 0.2, duration: 0.6 }}
                    >
                      <span className="text-xs">▶</span>
                    </motion.div>
                  )}

                  {/* Terminal Command Display */}
                  <div className="flex items-center w-full">
                    {/* Expanded: Clean command text only (no icons, no $ prompt, no cursor) */}
                    <div className="flex items-center flex-1">
                      <span
                        className={cn(
                          'transition-all duration-300 font-mono text-sm',
                          isActive ? 'text-green-300' : 'text-green-400',
                          hoveredItem === item.name && 'text-green-300',
                        )}
                      >
                        {item.command}
                      </span>
                    </div>
                  </div>


                </Link>
              </motion.div>
            );
          })}
        </nav>

        {/* Terminal Prompt Section - Moved below navigation */}
        <div className="bg-black bg-opacity-95 border-t border-gray-600 p-3">
          <AnimatePresence mode="wait">
            {true && (
              <motion.div
                key="expanded-prompt"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="font-mono text-xs space-y-1"
              >
                {/* Command History */}
                {commandHistory.map((cmd, index) => (
                  <motion.div
                    key={`${cmd}-${index}`}
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-gray-500"
                  >
                    <span className="text-green-300">user</span>
                    <span className="text-gray-400">@</span>
                    <span className="text-blue-400">portfolio</span>
                    <span className="text-gray-400">:</span>
                    <span className="text-yellow-400">~</span>
                    <span className="text-green-400">$ </span>
                    <span className="text-gray-300">{cmd}</span>
                  </motion.div>
                ))}

                {/* Current Command Line */}
                <div className="text-green-400">
                  <span className="text-green-300">user</span>
                  <span className="text-gray-400">@</span>
                  <span className="text-blue-400">portfolio</span>
                  <span className="text-gray-400">:</span>
                  <span className="text-yellow-400">~</span>
                  <span className="text-green-400">$ </span>
                  <span className="text-gray-300">{currentCommand}</span>
                  <motion.span
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-green-400"
                  >
                    ▋
                  </motion.span>
                </div>

                {!isTyping && commandHistory.length === 0 && (
                  <div className="text-gray-400 text-xs mt-2">Available commands:</div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Terminal Footer */}
        <div className="mt-auto bg-black bg-opacity-95">
          {' '}
          {/* Terminal footer background */}
          {/* Terminal Separator */}
          <div className="px-4 py-2">
            <div className="h-px bg-gradient-to-r from-transparent via-green-400/30 to-transparent"></div>
          </div>
          <div className="p-3">
            <div className="space-y-3">
              {/* Terminal Social Commands */}
              <div className="flex font-mono text-xs grid grid-cols-2 gap-1"
              >
                {[
                  {
                    icon: Mail,
                    label: 'mail',
                    command: '$ mail',
                    href: 'mailto:<EMAIL>',
                  },
                  {
                    icon: Linkedin,
                    label: 'linkedin',
                    command: '$ linkedin',
                    href: 'https://linkedin.com',
                  },
                  {
                    icon: Github,
                    label: 'github',
                    command: '$ github',
                    href: 'https://github.com',
                  },
                ].map(social => {
                  const IconComponent = social.icon;
                  return (
                    <motion.a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center transition-all duration-200 rounded border border-transparent group relative hover:bg-green-400/10 hover:border-green-400/30 focus:outline-none focus:ring-2 focus:ring-green-400/50 text-green-400 hover:text-green-300 justify-start space-x-1 p-1"
                      aria-label={`Execute ${social.command}`}
                    >
                      <span className="text-green-400">$</span>
                      <IconComponent
                        size={12}
                        className="transition-colors duration-200"
                      />
                      <span className="transition-colors duration-200">
                        {social.label}
                      </span>
                      </AnimatePresence>
                    </motion.a>
                  );
                })}
              </div>

              {/* Terminal System Info */}
              <div className="text-center font-mono text-xs">
                <AnimatePresence mode="wait">
                  {isCollapsed ? (
                    <motion.div
                      key="collapsed-info"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      className="flex flex-col items-center space-y-2"
                    >
                      {/* Minimal version info */}
                      <div className="text-gray-500 text-xs">v2.0</div>

                      {/* Terminal status indicator */}
                      <div className="flex items-center space-x-1">
                        <motion.div
                          className="w-1.5 h-1.5 bg-green-400 rounded-full"
                          animate={{
                            opacity: [0.5, 1, 0.5],
                            scale: [1, 1.2, 1],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: 'easeInOut',
                          }}
                        />
                        <span className="text-green-400 text-xs">ON</span>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="expanded-info"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-gray-500 space-y-1"
                    >
                      <div className="text-green-400 text-xs">System ready</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </aside>





      {/* Geometric Pattern Background */}
      <pattern id="hexPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
        <polygon
          points="20,5 30,15 30,25 20,35 10,25 10,15"
          fill="none"
          stroke="#2CD3C5"
          strokeWidth="0.5"
          opacity="0.1"
        />
      </pattern>

      <rect width="220" height="100%" fill="url(#hexPattern)" />
      {/* Floating Geometric Particles */}
      <div
        className="fixed left-0 top-0 w-[220px] h-full pointer-events-none overflow-hidden"
        style={{ zIndex: 'calc(var(--z-sidebar) - 2)' }}
      >
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-accent-primary/20 diamond"
            style={{
              left: `${20 + i * 40}px`,
              top: `${100 + i * 60}px`,
            }}
            animate={{
              y: [-10, 10, -10],
              rotate: [0, 180, 360],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.5,
            }}
          />
        ))}
      </div>
    </>
  );
}
