const bcrypt = require('bcryptjs');

async function generateHash() {
  const password = process.argv[2] || 'admin123';
  console.log('Generating hash for password:', password);
  
  const hash = await bcrypt.hash(password, 12);
  console.log('Generated hash:', hash);
  console.log('');
  console.log('Add this to your .env.local file:');
  console.log(`ADMIN_PASSWORD_HASH=${hash}`);
  
  // Test the hash
  const isValid = await bcrypt.compare(password, hash);
  console.log('');
  console.log('Hash verification test:', isValid ? 'PASSED' : 'FAILED');
}

generateHash().catch(console.error);
