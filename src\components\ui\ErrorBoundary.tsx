'use client';

import { motion } from 'framer-motion';
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';
import type { ReactNode } from 'react';
import React, { Component } from 'react';

import type { AppError } from '@/types';

import Button from './Button';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  isolate?: boolean;
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
}

interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  hasError: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  eventId?: string;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: undefined,
      errorInfo: undefined,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const eventId = this.logError(error, errorInfo);

    this.setState({
      error,
      errorInfo,
      eventId,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key)) {
        this.resetError();
      }
    }

    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetError();
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      window.clearTimeout(this.resetTimeoutId);
    }
  }

  private logError = (error: Error, errorInfo: React.ErrorInfo): string => {
    const eventId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const errorData: AppError = {
      code: error.name || 'UnknownError',
      message: error.message,
      details: {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      },
      timestamp: new Date().toISOString(),
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Data:', errorData);
      console.groupEnd();
    }

    // In production, you might want to send this to an error reporting service
    // Example: Sentry, LogRocket, etc.
    if (process.env.NODE_ENV === 'production') {
      // sendErrorToService(errorData);
    }

    return eventId;
  };

  private resetError = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      eventId: undefined,
    });
  };

  private handleRetry = () => {
    this.resetError();

    // Add a small delay to prevent immediate re-error
    this.resetTimeoutId = window.setTimeout(() => {
      // Force a re-render
      this.forceUpdate();
    }, 100);
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback: Fallback, isolate } = this.props;

    if (hasError && error) {
      if (Fallback) {
        return <Fallback error={error} resetError={this.handleRetry} hasError={hasError} />;
      }

      return (
        <DefaultErrorFallback
          error={error}
          resetError={this.handleRetry}
          hasError={hasError}
          isolate={isolate}
        />
      );
    }

    return children;
  }
}

interface DefaultErrorFallbackProps extends ErrorFallbackProps {
  isolate?: boolean;
}

function DefaultErrorFallback({ error, resetError, isolate = false }: DefaultErrorFallbackProps) {
  const containerClass = isolate
    ? 'bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-6'
    : 'min-h-screen bg-background-light dark:bg-background-dark flex items-center justify-center p-6';

  return (
    <div className={containerClass}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`max-w-md w-full ${isolate ? '' : 'bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-8'} text-center`}
      >
        <div className="w-16 h-16 mx-auto mb-6 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-8 h-8 text-red-500" />
        </div>

        <h2 className="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
          {isolate ? 'Component Error' : 'Something went wrong'}
        </h2>

        <p className="text-text-secondary-light dark:text-text-secondary-dark mb-6">
          {error.message || 'An unexpected error occurred. Please try again.'}
        </p>

        {process.env.NODE_ENV === 'development' && (
          <details className="mb-6 text-left">
            <summary className="cursor-pointer text-sm text-red-600 dark:text-red-400 mb-2">
              <Bug className="inline w-4 h-4 mr-1" />
              Error Details (Development)
            </summary>
            <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto max-h-32">
              {error.stack}
            </pre>
          </details>
        )}

        <div className="flex gap-3 justify-center">
          <Button
            onClick={resetError}
            variant="primary"
            icon={<RefreshCw className="w-4 h-4" />}
            size="sm"
          >
            Try Again
          </Button>

          {!isolate && (
            <Button
              onClick={() => (window.location.href = '/')}
              variant="outline"
              icon={<Home className="w-4 h-4" />}
              size="sm"
            >
              Go Home
            </Button>
          )}
        </div>
      </motion.div>
    </div>
  );
}

// Higher-order component for easy wrapping
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>,
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Hook for manual error reporting
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: Record<string, unknown>) => {
    // Create a synthetic error boundary-like error
    const syntheticError = new Error(error.message);
    syntheticError.name = error.name;
    syntheticError.stack = error.stack;

    // Log the error
    console.error('Manual error report:', error, errorInfo);

    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // sendErrorToService({ error, errorInfo });
    }
  }, []);
}

export default ErrorBoundary;
