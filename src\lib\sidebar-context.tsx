'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface SidebarContextType {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  setIsMobileOpen: (open: boolean) => void;
  toggleCollapse: () => void;
  toggleMobile: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileOpen(false);
      }
      // Auto-collapse on smaller screens for better mobile experience
      if (window.innerWidth < 1024) {
        setIsCollapsed(true);
      } else if (window.innerWidth >= 1024) {
        // Only auto-expand on large screens if not manually collapsed
        const wasManuallyCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        if (!wasManuallyCollapsed) {
          setIsCollapsed(false);
        }
      }
    };

    // Set initial state based on screen size and user preference
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update CSS custom property when sidebar state changes
  useEffect(() => {
    if (!mounted) {
      return;
    }

    const root = document.documentElement;

    if (window.innerWidth < 768) {
      // Mobile: no margin when closed, full width when open
      root.style.setProperty('--sidebar-width', '0px');
    } else if (window.innerWidth < 1024) {
      // Tablet: always collapsed
      root.style.setProperty('--sidebar-width', 'var(--sidebar-width-collapsed)');
    } else {
      // Desktop: responsive to collapse state
      root.style.setProperty(
        '--sidebar-width',
        isCollapsed ? 'var(--sidebar-width-collapsed)' : 'var(--sidebar-width-expanded)',
      );
    }
  }, [isCollapsed, isMobileOpen, mounted]);

  const toggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    // Remember user preference
    localStorage.setItem('sidebar-collapsed', newCollapsedState.toString());
  };

  const toggleMobile = () => setIsMobileOpen(!isMobileOpen);

  const contextValue = {
    isCollapsed,
    isMobileOpen,
    setIsCollapsed,
    setIsMobileOpen,
    toggleCollapse,
    toggleMobile,
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {mounted ? children : <div className="opacity-0">{children}</div>}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
