'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface SidebarContextType {
  isMobileOpen: boolean;
  setIsMobileOpen: (open: boolean) => void;
  toggleMobile: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileOpen(false);
      }
    };

    // Set initial state based on screen size
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update CSS custom property when sidebar state changes
  useEffect(() => {
    if (!mounted) {
      return;
    }

    const root = document.documentElement;

    if (window.innerWidth < 768) {
      // Mobile: no margin when closed, full width when open
      root.style.setProperty('--sidebar-width', '0px');
    } else {
      // Desktop: always expanded
      root.style.setProperty('--sidebar-width', 'var(--sidebar-width-expanded)');
    }
  }, [isMobileOpen, mounted]);

  const toggleMobile = () => setIsMobileOpen(!isMobileOpen);

  const contextValue = {
    isMobileOpen,
    setIsMobileOpen,
    toggleMobile,
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {mounted ? children : <div className="opacity-0">{children}</div>}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
