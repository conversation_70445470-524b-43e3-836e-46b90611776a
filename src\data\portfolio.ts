import type { PersonalInfo, Project, Skill, Experience, Education } from '@/types';
import { SocialLink } from '@/types';

export const personalInfo: PersonalInfo = {
  name: '<PERSON><PERSON><PERSON>',
  title: 'Software Engineer',
  tagline: 'Crafting scalable, elegant solutions with code',
  bio: 'Passionate software engineer with a fresh perspective on modern development. I love building user-centric applications that solve real-world problems through clean, efficient code and thoughtful design.',
  location: 'Samsun, Turkey',
  email: '<EMAIL>',
  resumeUrl: '/resume.pdf',
  socialLinks: [
    {
      name: 'GitHub',
      url: 'https://github.com/ehsan-amini',
      icon: 'github',
      color: '#333',
    },
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/in/ehsan-amini',
      icon: 'linkedin',
      color: '#0077B5',
    },
    {
      name: 'Email',
      url: 'mailto:<EMAIL>',
      icon: 'mail',
      color: '#EA4335',
    },
  ],
};

export const skills: Skill[] = [];

export const projects: Project[] = [];

export const education: Education[] = [];

export const experience: Experience[] = [];

export const seoConfig = {
  title: '<PERSON><PERSON><PERSON> - Software Engineer Portfolio',
  description:
    'Software Engineer specializing in modern web development. Crafting scalable, elegant solutions with React, Node.js, and Python.',
  keywords: [
    'Software Engineer',
    'Web Developer',
    'React',
    'Node.js',
    'Python',
    'Full Stack Developer',
    'Ehsan Amini',
  ],
  author: 'Ehsan Amini',
  siteUrl: 'https://ehsan-amini.vercel.app',
  image: '/og-image.jpg',
};

// Combined portfolio data export for admin panel
export const portfolioData = {
  personal: personalInfo,
  skills,
  projects,
  education,
  experience,
  seo: seoConfig,
};
