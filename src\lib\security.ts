// Security utilities and input sanitization

// XSS Protection
export function sanitizeHtml(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }

  const entityMap: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;',
  };

  return input.replace(/[&<>"'`=\/]/g, s => entityMap[s] || s);
}

// SQL Injection Protection (for client-side validation)
export function sanitizeSqlInput(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }

  // Remove or escape potentially dangerous SQL characters
  return input
    .replace(/'/g, "''") // Escape single quotes
    .replace(/;/g, '') // Remove semicolons
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove block comment start
    .replace(/\*\//g, '') // Remove block comment end
    .replace(/xp_/gi, '') // Remove extended procedures
    .replace(/sp_/gi, '') // Remove stored procedures
    .trim();
}

// Input validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s\-_]+$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
} as const;

// Input sanitization functions
export function sanitizeInput(
  input: string,
  type: 'text' | 'email' | 'url' | 'alphanumeric' = 'text',
): string {
  if (typeof input !== 'string') {
    return '';
  }

  let sanitized = input.trim();

  switch (type) {
    case 'email':
      sanitized = sanitized.toLowerCase();
      break;
    case 'url':
      sanitized = sanitized.toLowerCase();
      if (!sanitized.startsWith('http://') && !sanitized.startsWith('https://')) {
        sanitized = 'https://' + sanitized;
      }
      break;
    case 'alphanumeric':
      sanitized = sanitized.replace(/[^a-zA-Z0-9]/g, '');
      break;
    case 'text':
    default:
      sanitized = sanitizeHtml(sanitized);
      break;
  }

  return sanitized;
}

// Content Security Policy helpers
export function generateNonce(): string {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  // Fallback for environments without crypto
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Rate limiting (client-side tracking)
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  constructor(
    private maxRequests: number = 10,
    private windowMs: number = 60000, // 1 minute
  ) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);

    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(identifier, validRequests);

    return true;
  }

  reset(identifier: string): void {
    this.requests.delete(identifier);
  }
}

export const rateLimiter = new RateLimiter();

// CSRF Token management
export function generateCSRFToken(): string {
  return generateNonce();
}

export function validateCSRFToken(token: string, expectedToken: string): boolean {
  if (!token || !expectedToken) {
    return false;
  }
  return token === expectedToken;
}

// Secure cookie helpers
export function setSecureCookie(
  name: string,
  value: string,
  options: {
    maxAge?: number;
    secure?: boolean;
    httpOnly?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    domain?: string;
    path?: string;
  } = {},
): void {
  const {
    maxAge = 86400, // 24 hours
    secure = process.env.NODE_ENV === 'production',
    httpOnly = true,
    sameSite = 'lax',
    domain,
    path = '/',
  } = options;

  let cookieString = `${name}=${encodeURIComponent(value)}`;
  cookieString += `; Max-Age=${maxAge}`;
  cookieString += `; Path=${path}`;
  cookieString += `; SameSite=${sameSite}`;

  if (secure) {
    cookieString += '; Secure';
  }
  if (httpOnly) {
    cookieString += '; HttpOnly';
  }
  if (domain) {
    cookieString += `; Domain=${domain}`;
  }

  document.cookie = cookieString;
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    feedback.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  if (!/[@$!%*?&]/.test(password)) {
    feedback.push('Password must contain at least one special character (@$!%*?&)');
  } else {
    score += 1;
  }

  // Check for common patterns
  if (/(.)\1{2,}/.test(password)) {
    feedback.push('Avoid repeating characters');
    score -= 1;
  }

  if (/123|abc|qwe/i.test(password)) {
    feedback.push('Avoid common sequences');
    score -= 1;
  }

  return {
    isValid: score >= 4 && feedback.length === 0,
    score: Math.max(0, Math.min(5, score)),
    feedback,
  };
}

// Secure random string generation
export function generateSecureRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => chars[byte % chars.length]).join('');
  }

  // Fallback for environments without crypto
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// URL validation and sanitization
export function validateAndSanitizeUrl(url: string): string | null {
  try {
    const sanitized = sanitizeInput(url, 'url');
    const urlObj = new URL(sanitized);

    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return null;
    }

    return urlObj.toString();
  } catch {
    return null;
  }
}

// File upload security
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

export function validateFileSize(file: File, maxSizeBytes: number): boolean {
  return file.size <= maxSizeBytes;
}

export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
    .toLowerCase();
}

// Security headers validation
export function validateSecurityHeaders(headers: Headers): {
  isSecure: boolean;
  missing: string[];
  recommendations: string[];
} {
  const requiredHeaders = [
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection',
    'strict-transport-security',
    'content-security-policy',
  ];

  const missing: string[] = [];
  const recommendations: string[] = [];

  requiredHeaders.forEach(header => {
    if (!headers.has(header)) {
      missing.push(header);
    }
  });

  // Check CSP
  const csp = headers.get('content-security-policy');
  if (csp && csp.includes('unsafe-inline')) {
    recommendations.push('Consider removing unsafe-inline from CSP');
  }

  // Check HSTS
  const hsts = headers.get('strict-transport-security');
  if (hsts && !hsts.includes('includeSubDomains')) {
    recommendations.push('Consider adding includeSubDomains to HSTS');
  }

  return {
    isSecure: missing.length === 0,
    missing,
    recommendations,
  };
}
