export interface Project {
  readonly id: string;
  title: string;
  description: string;
  longDescription?: string;
  technologies: readonly string[];
  category: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'other';
  image: string;
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  year: number;
  status: 'completed' | 'in-progress' | 'planned';
  createdAt?: string;
  updatedAt?: string;
}

export interface Skill {
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'languages' | 'other';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  icon?: string;
  color?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  description: string[];
  technologies: string[];
  type: 'work' | 'internship' | 'freelance' | 'project';
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  duration: string;
  gpa?: string;
  achievements?: string[];
}

export interface ContactForm {
  name: string;
  email: string;
  subject?: string;
  message: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color?: string;
}

export interface PersonalInfo {
  name: string;
  title: string;
  tagline: string;
  bio: string;
  location: string;
  email: string;
  phone?: string;
  resumeUrl: string;
  profileImage?: string;
  socialLinks: SocialLink[];
}

export interface ThemeConfig {
  isDark: boolean;
  primaryColor: string;
  accentColor: string;
}

export interface AnimationConfig {
  duration: number;
  delay: number;
  easing: string;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error handling types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

// Form types
export interface FormFieldError {
  message: string;
  type: string;
}

export interface FormState<T = Record<string, unknown>> {
  data: T;
  errors: Record<keyof T, FormFieldError | undefined>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}



// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

export interface LoadingState {
  isLoading: boolean;
  loadingText?: string;
}

export interface AsyncState<T> extends LoadingState {
  data?: T;
  error?: string;
  lastUpdated?: string;
}

// Navigation types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
  isExternal?: boolean;
}

// Theme types
export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  border: string;
}

export interface ThemeColors {
  light: ColorScheme;
  dark: ColorScheme;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event handler types
export type EventHandler<T = Event> = (event: T) => void;
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

// Storage types
export interface StorageItem<T> {
  value: T;
  timestamp: number;
  expiresAt?: number;
}

// Validation types
export interface ValidationRule<T = unknown> {
  validate: (value: T) => boolean | string;
  message: string;
}

export type ValidationSchema<T> = {
  [K in keyof T]: ValidationRule<T[K]>[];
};

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  author: string;
  siteUrl: string;
  image?: string;
}
